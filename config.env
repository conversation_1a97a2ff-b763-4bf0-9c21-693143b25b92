# OANDA API Configuration
OANDA_API_KEY=your_oanda_api_key_here
OANDA_ACCOUNT_ID=your_account_id_here
OANDA_ENVIRONMENT=practice
# Use 'practice' for demo trading, 'live' for real money

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Trading Configuration
DEFAULT_CURRENCY_PAIR=EUR_USD
DEFAULT_TRADE_AMOUNT=1000
MAX_DAILY_LOSS=100
MAX_OPEN_POSITIONS=3
STOP_LOSS_PIPS=20
TAKE_PROFIT_PIPS=40

# Risk Management
MAX_RISK_PER_TRADE=2
# Maximum percentage of account to risk per trade

# Trading Schedule (24-hour format)
TRADING_START_HOUR=8
TRADING_END_HOUR=18
TIMEZONE=UTC
